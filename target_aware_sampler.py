#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
Target-Aware Batch Sampler for Stage 2 Training

This module provides batch sampling strategies that ensure target-specific 
context relevance during training. It prevents mixing samples from different
targets within the same batch, which is crucial for context-enriched learning.
"""

import random
import torch
from torch.utils.data import <PERSON><PERSON>, BatchSampler
from typing import Dict, List, Iterator, Optional
from collections import defaultdict
import logging


class TargetGroupedBatchSampler(BatchSampler):
    """
    Batch sampler that groups samples by target ID to ensure context relevance.
    
    This sampler ensures that each batch contains samples from only one target,
    which is essential for target-specific context learning in Stage 2 training.
    """
    
    def __init__(self, 
                 dataset,
                 batch_size: int,
                 shuffle: bool = True,
                 drop_last: bool = False,
                 min_samples_per_target: int = 1):
        """
        Args:
            dataset: TargetContextDataset with samples grouped by target
            batch_size: Maximum number of samples per batch
            shuffle: Whether to shuffle targets and samples within targets
            drop_last: Whether to drop the last incomplete batch
            min_samples_per_target: Minimum samples required per target to include
        """
        self.dataset = dataset
        self.batch_size = batch_size
        self.shuffle = shuffle
        self.drop_last = drop_last
        self.min_samples_per_target = min_samples_per_target
        
        # Group sample indices by target
        self.target_to_indices = defaultdict(list)
        self._group_indices_by_target()
        
        # Filter targets with sufficient samples
        self.valid_targets = [
            target for target, indices in self.target_to_indices.items()
            if len(indices) >= self.min_samples_per_target
        ]
        
        logging.info(f"TargetGroupedBatchSampler: {len(self.valid_targets)} valid targets, "
                    f"batch_size={batch_size}, shuffle={shuffle}")
    
    def _group_indices_by_target(self):
        """Group dataset indices by their target IDs"""
        for idx, sample in enumerate(self.dataset.samples):
            target_id = sample['target_id']
            self.target_to_indices[target_id].append(idx)
    
    def __iter__(self) -> Iterator[List[int]]:
        """Generate batches of indices grouped by target"""
        if self.shuffle:
            # Shuffle the order of targets
            targets = self.valid_targets.copy()
            random.shuffle(targets)
        else:
            targets = self.valid_targets
        
        for target_id in targets:
            indices = self.target_to_indices[target_id].copy()
            
            if self.shuffle:
                # Shuffle samples within each target
                random.shuffle(indices)
            
            # Create batches for this target
            for i in range(0, len(indices), self.batch_size):
                batch_indices = indices[i:i + self.batch_size]
                
                # Skip incomplete batches if drop_last is True
                if self.drop_last and len(batch_indices) < self.batch_size:
                    continue
                
                yield batch_indices
    
    def __len__(self) -> int:
        """Calculate total number of batches"""
        total_batches = 0
        for target_id in self.valid_targets:
            target_samples = len(self.target_to_indices[target_id])
            if self.drop_last:
                target_batches = target_samples // self.batch_size
            else:
                target_batches = (target_samples + self.batch_size - 1) // self.batch_size
            total_batches += target_batches
        return total_batches


class EpisodicTargetSampler(Sampler):
    """
    Episodic sampler for few-shot learning style training.
    
    Each episode focuses on one target with support (context) and query sets.
    This is more aligned with meta-learning approaches for target-specific screening.
    """
    
    def __init__(self,
                 dataset,
                 n_episodes: int,
                 n_support: int,
                 n_query: int,
                 shuffle: bool = True):
        """
        Args:
            dataset: TargetContextDataset
            n_episodes: Number of episodes per epoch
            n_support: Number of support samples per episode (context)
            n_query: Number of query samples per episode (to predict)
            shuffle: Whether to shuffle targets and samples
        """
        self.dataset = dataset
        self.n_episodes = n_episodes
        self.n_support = n_support
        self.n_query = n_query
        self.shuffle = shuffle
        
        # Group samples by target
        self.target_to_indices = defaultdict(list)
        self._group_indices_by_target()
        
        # Filter targets with sufficient samples
        min_samples = n_support + n_query
        self.valid_targets = [
            target for target, indices in self.target_to_indices.items()
            if len(indices) >= min_samples
        ]
        
        logging.info(f"EpisodicTargetSampler: {len(self.valid_targets)} valid targets, "
                    f"{n_episodes} episodes, {n_support} support + {n_query} query")
    
    def _group_indices_by_target(self):
        """Group dataset indices by target IDs"""
        for idx, sample in enumerate(self.dataset.samples):
            target_id = sample['target_id']
            self.target_to_indices[target_id].append(idx)
    
    def __iter__(self) -> Iterator[List[int]]:
        """Generate episodes with support and query sets"""
        for episode in range(self.n_episodes):
            # Sample a target for this episode
            target_id = random.choice(self.valid_targets)
            target_indices = self.target_to_indices[target_id].copy()
            
            if self.shuffle:
                random.shuffle(target_indices)
            
            # Ensure we have enough samples
            if len(target_indices) < self.n_support + self.n_query:
                # Sample with replacement if needed
                target_indices = random.choices(target_indices, 
                                              k=self.n_support + self.n_query)
            
            # Split into support and query
            support_indices = target_indices[:self.n_support]
            query_indices = target_indices[self.n_support:self.n_support + self.n_query]
            
            # Return combined indices (support + query)
            # The dataset/model will need to know which are support vs query
            episode_indices = support_indices + query_indices
            yield episode_indices
    
    def __len__(self) -> int:
        return self.n_episodes


class BalancedTargetSampler(Sampler):
    """
    Balanced sampler that ensures equal representation of targets across epochs.
    
    This sampler tries to balance the number of samples seen from each target
    during training, which is useful when targets have very different sample counts.
    """
    
    def __init__(self,
                 dataset,
                 samples_per_target: Optional[int] = None,
                 shuffle: bool = True):
        """
        Args:
            dataset: TargetContextDataset
            samples_per_target: Number of samples per target per epoch (None for all)
            shuffle: Whether to shuffle samples
        """
        self.dataset = dataset
        self.samples_per_target = samples_per_target
        self.shuffle = shuffle
        
        # Group samples by target
        self.target_to_indices = defaultdict(list)
        self._group_indices_by_target()
        
        # Determine sampling strategy
        if samples_per_target is None:
            # Use all samples
            self.samples_per_target = max(len(indices) for indices in self.target_to_indices.values())
        
        logging.info(f"BalancedTargetSampler: {len(self.target_to_indices)} targets, "
                    f"{self.samples_per_target} samples per target")
    
    def _group_indices_by_target(self):
        """Group dataset indices by target IDs"""
        for idx, sample in enumerate(self.dataset.samples):
            target_id = sample['target_id']
            self.target_to_indices[target_id].append(idx)
    
    def __iter__(self) -> Iterator[int]:
        """Generate balanced samples across targets"""
        all_indices = []
        
        for target_id, indices in self.target_to_indices.items():
            target_indices = indices.copy()
            
            if self.shuffle:
                random.shuffle(target_indices)
            
            # Sample or repeat to get desired number of samples
            if len(target_indices) >= self.samples_per_target:
                # Subsample
                sampled_indices = target_indices[:self.samples_per_target]
            else:
                # Repeat samples to reach target count
                repeats = self.samples_per_target // len(target_indices)
                remainder = self.samples_per_target % len(target_indices)
                
                sampled_indices = target_indices * repeats + target_indices[:remainder]
            
            all_indices.extend(sampled_indices)
        
        if self.shuffle:
            random.shuffle(all_indices)
        
        return iter(all_indices)
    
    def __len__(self) -> int:
        return len(self.target_to_indices) * self.samples_per_target