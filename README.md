# Stage 2 Training Framework

## Overview
This directory contains the complete Stage 2 training framework for context-enhanced ligand screening using EPT (Equivariant Pretrained Transformer) with context enrichment for improved predictions.

## Directory Structure

```
stage2/
├── train_stage2.py          # Main training script
├── stage2_config.yaml       # Configuration file
├── ept_context_wrapper.py   # EPT+Context model wrapper
├── context_module.py        # Context enrichment reasoning module
├── target_aware_sampler.py  # Target-aware batch sampling
└── README.md               # This documentation
```

## Core Components

### 1. **train_stage2.py** - Main Training Script
- **Purpose**: Orchestrates the entire Stage 2 training pipeline
- **Key Classes**:
  - `Stage2ContextDataset`: Loads and processes stage2 context-target pairs
  - `Stage2Trainer`: Manages training loop, optimization, and checkpointing

**Data Flow**:
```
Stage2 Data → Stage2ContextDataset → Target-Aware Batching → EPT+Context Model → Predictions
```

### 2. **stage2_config.yaml** - Configuration
- **EPT Model**: Stage 1 (frozen) configuration
- **Context Model**: Context enrichment parameters
- **Training**: Optimization settings
- **Data**: Paths and context configuration

### 3. **ept_context_wrapper.py** - Model Integration
- **Purpose**: Wraps EPT backbone with context enrichment module
- **Key Features**:
  - Freezes Stage 1 EPT parameters
  - Integrates context-aware reasoning
  - Manages context banks for each target

### 4. **context_module.py** - Context Reasoning
- **Purpose**: Implements context-aware prediction using retrieved contexts
- **Key Components**:
  - Context retrieval based on similarity
  - Attention-based context fusion
  - Evidence aggregation for final predictions

### 5. **target_aware_sampler.py** - Batch Sampling
- **Purpose**: Ensures homogeneous target batches for efficient context processing
- **Key Features**:
  - Groups samples by UniProt target
  - Maintains consistent context across batch
  - Supports episodic training paradigm

## Usage

### Step 1: Generate Stage2 Data
```bash
python /home/<USER>/colossus_code/EPT/benchmark_evaluation/create_block_interface_stage_2.py
```

### Step 2: Train Stage2 Model
```bash
python train_stage2.py \
    --config stage2_config.yaml \
    --stage1_ckpt /path/to/stage1/checkpoint.ckpt \
    --output_dir /path/to/output
```

## Data Format

### Input (from create_block_interface_stage_2.py)
```json
{
  "pair_id": "A0A024B7W1__6ld2_KY3_A_to_5ulp_KB1_B_context_vs_active",
  "uniprot_id": "A0A024B7W1",
  "context_key": "6ld2_KY3_A",
  "target_key": "5ulp_KB1_B", 
  "pair_type": "context_vs_active",
  "label": 1,
  "context_data": {...},  # EPT blocks for context ligand
  "target_data": {...}    # EPT blocks for target ligand
}
```

### Processing
1. **Context Banking**: Groups contexts by UniProt ID
2. **Target Grouping**: Organizes prediction targets by protein
3. **Context Registration**: Registers context banks with EPT-RAG model
4. **Batch Formation**: Creates homogeneous target batches

## Model Architecture

```
Input: Target Ligand + Context Bank

┌─────────────────────────────────────────────────────────────┐
│                    EPT Backbone (Frozen)                    │
├─────────────────────────────────────────────────────────────┤
│  Context Encoder │            │ Query Encoder              │
│  (contexts)      │            │ (target ligand)            │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                Context Enrichment Module                    │
├─────────────────────────────────────────────────────────────┤
│  • Context Retrieval (similarity-based)                    │
│  • Context Attention (multi-head)                          │
│  • Evidence Fusion                                         │
│  • Context-aware Prediction                                │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
                         Predictions
```

## Key Features

### 1. **Context-Engine Fusion**
- Uses best resolution structure (context) to enhance prediction
- Retrieves most relevant contexts for each query
- Attention-based fusion of context information

### 2. **Target-Aware Training**
- Homogeneous batches ensure consistent context
- Episodic training paradigm for better generalization
- Efficient processing of target-specific contexts

### 3. **Flexible Architecture**
- Supports variable context sizes
- Adaptable to different protein families
- Scalable to large context banks

## Configuration Parameters

### Context Configuration
```yaml
context_config:
  min_actives: 2           # Minimum active samples per UniProt
  min_inactives: 2         # Minimum inactive samples per UniProt  
  min_total: 5             # Minimum total samples per UniProt
  max_context_size: 20     # Maximum contexts per training sample
```

### Context Model Parameters
```yaml
context_model:
  hidden_size: 512         # Must match EPT encoder
  max_context_size: 100    # Maximum contexts per target
  retrieval_top_k: 15      # Top-k contexts to retrieve
  num_reasoning_layers: 3  # Context reasoning depth
```

## Training Process

1. **Data Loading**: Load stage2 context-target pairs
2. **Context Banking**: Organize contexts by UniProt targets
3. **Context Registration**: Register context banks with model
4. **Batch Formation**: Create target-aware batches  
5. **Training Loop**: Train context enrichment module while keeping EPT frozen
6. **Validation**: Evaluate on held-out context-target pairs

## Expected Outputs

- **Model Checkpoints**: Saved in output directory
- **Training Logs**: Detailed logging of training progress
- **Metrics**: AUC, AP, and loss tracking
- **Best Model**: Automatically saved based on validation performance

## Performance Considerations

- **Memory**: Context banks require additional memory
- **Compute**: Attention over contexts increases computation
- **Batching**: Target-aware batching may affect batch utilization
- **Scalability**: Designed for large-scale protein screening

## Dependencies

- PyTorch
- NumPy, Pandas
- Transformers (attention mechanisms)
- EPT framework components
- Custom data loaders and samplers