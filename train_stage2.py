#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
Stage 2 Training: Context-Aware Ligand Screening

This script trains the Stage 2 context module on top of frozen EPT embeddings.
It implements the complete pipeline for context-aware virtual screening.

Usage:
    python train_stage2.py --config config.yaml --stage1_ckpt path/to/ept/model.ckpt
"""

import os
import sys
import argparse
import yaml
import json
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
from tqdm import tqdm
import logging
from sklearn.metrics import roc_auc_score, average_precision_score, precision_recall_curve
from collections import defaultdict, namedtuple
import random
from typing import Dict, List, Optional, Tuple

# Add EPT to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ept_context_wrapper import EPTContextWrapper
from target_aware_sampler import TargetGroupedBatchSampler
from data.dataset_cls import CLSDataset
from utils.logger import print_log


def setup_logging(log_dir: str):
    """Setup logging configuration"""
    os.makedirs(log_dir, exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'train_stage2.log')),
            logging.StreamHandler()
        ]
    )


class Stage2ContextDataset(Dataset):
    """
    Dataset for Stage 2 training with context-target pairs
    
    This dataset works directly with the stage2 data format from create_block_interface_stage_2.py
    which contains context-target pairs with embedded context and target data.
    """
    
    def __init__(self, 
                 stage2_dataset_path: str,
                 context_config: Dict,
                 mode: str = 'train',
                 training_phase: str = 'stage2_only'):
        """
        Args:
            stage2_dataset_path: Path to stage2 mmap dataset with context-target pairs
            context_config: Configuration for context handling
            mode: 'train' or 'val'
            training_phase: 'stage2_only' or 'joint_training' for multi-stage support
        """
        from data.mmap_dataset import MMAPDataset
        
        self.stage2_dataset = MMAPDataset(stage2_dataset_path)
        self.context_config = context_config
        self.mode = mode
        self.training_phase = training_phase
        
        # Group data by UniProt targets and extract contexts
        self.uniprot_groups = defaultdict(list)
        self.context_banks = {}
        self._process_stage2_data()
        
        # Filter valid targets and create training samples
        self.valid_targets = self._filter_valid_targets()
        self.samples = []
        self._create_training_samples()
        
        print_log(f"Created {len(self.samples)} training samples from {len(self.valid_targets)} UniProt targets")
        print_log(f"Training phase: {self.training_phase}")
    
    def _process_stage2_data(self):
        """Process stage2 data and group by UniProt targets"""
        print_log("Processing stage2 context-target pairs...")

        for idx, stage2_data in tqdm(enumerate(self.stage2_dataset)):
            # Debug: Print first few samples to understand data structure
            if idx < 3:
                print_log(f"Sample {idx} keys: {list(stage2_data.keys())}")
                print_log(f"Sample {idx} data: {stage2_data}")

            uniprot_id = stage2_data['uniprot_id']
            context_key = stage2_data['context_key']
            target_key = stage2_data['target_key']
            label = stage2_data['label']
            pair_type = stage2_data['pair_type']
            context_data = stage2_data['context_data']
            target_data = stage2_data['target_data']
            
            # Store context data (keyed by context_key to avoid duplicates)
            if uniprot_id not in self.context_banks:
                self.context_banks[uniprot_id] = {}
            
            if context_key not in self.context_banks[uniprot_id]:
                self.context_banks[uniprot_id][context_key] = context_data
            
            # Group target data by UniProt - FIX: Include label in sample data
            self.uniprot_groups[uniprot_id].append({
                'target_key': target_key,
                'context_key': context_key,
                'label': label,  # FIX: Store the label
                'pair_type': pair_type,
                'target_data': target_data
            })
        
        print_log(f"Processed {len(self.stage2_dataset)} pairs for {len(self.uniprot_groups)} UniProt targets")
        
    def _filter_valid_targets(self) -> List[str]:
        """Filter UniProt targets that have sufficient data for context training"""
        valid_targets = []
        min_actives = self.context_config.get('min_actives', 2)
        min_inactives = self.context_config.get('min_inactives', 2)
        min_total = self.context_config.get('min_total', 5)
        
        for uniprot_id, samples in self.uniprot_groups.items():
            actives = [s for s in samples if s['label'] == 1]
            inactives = [s for s in samples if s['label'] == 0]
            
            if (len(actives) >= min_actives and 
                len(inactives) >= min_inactives and
                len(samples) >= min_total):
                valid_targets.append(uniprot_id)
                
        print_log(f"Found {len(valid_targets)} valid UniProt targets out of {len(self.uniprot_groups)} total")
        return valid_targets
        
    def _create_training_samples(self):
        """Create training samples from stage2 data"""
        for uniprot_id in self.valid_targets:
            samples = self.uniprot_groups[uniprot_id]
            
            # All samples for this UniProt are training samples
            # Context is provided via context_banks
            for sample in samples:
                self.samples.append({
                    'target_id': uniprot_id,
                    'target_key': sample['target_key'],
                    'context_key': sample['context_key'],
                    'pair_type': sample['pair_type'],
                    'label': sample['label'],  # FIX: Include label in sample
                    'target_data': sample['target_data']
                })
                
        print_log(f"Created {len(self.samples)} training samples")
    
    def set_training_phase(self, phase: str):
        """Update training phase for multi-stage training"""
        self.training_phase = phase
        print_log(f"Training phase updated to: {phase}")
        
    def __len__(self):
        return len(self.samples)
        
    def __getitem__(self, idx):
        sample = self.samples[idx]

        # Debug: Check if label exists in sample
        if idx < 3:
            print_log(f"Sample {idx} keys: {list(sample.keys())}")
            print_log(f"Sample {idx} has label: {'label' in sample}")
            if 'label' in sample:
                print_log(f"Sample {idx} label: {sample['label']}")

        # Get target data (query)
        query_data = sample['target_data'].copy()

        # Check if label exists in sample before accessing it
        if 'label' not in sample:
            print_log(f"ERROR: No label found in sample {idx}. Sample keys: {list(sample.keys())}")
            raise KeyError(f"Label not found in sample {idx}")

        query_data['label'] = [sample['label']]  # Add label in EPT format
        
        # Get all context data for this UniProt target
        uniprot_id = sample['target_id']
        context_bank = self.context_banks[uniprot_id]
        
        # Convert context bank to list format
        context_data = []
        context_labels = []
        
        for context_key, ctx_data in context_bank.items():  
            # Clean context data (remove label if present)
            ctx_data_clean = {k: v for k, v in ctx_data.items() if k != 'label'}
            context_data.append(ctx_data_clean)
            # Context is always from the best resolution structure (high quality)
            context_labels.append(1)  # Assuming context is always high-quality
        
        # Limit context size if too large
        max_context = self.context_config.get('max_context_size', 20)
        if len(context_data) > max_context:
            # Randomly sample contexts
            sampled_indices = random.sample(range(len(context_data)), max_context)
            context_data = [context_data[i] for i in sampled_indices]
            context_labels = [context_labels[i] for i in sampled_indices]
            
        return {
            'target_id': uniprot_id,  # Use UniProt ID as target ID
            'query_data': query_data,
            'context_data': context_data,
            'context_labels': context_labels,
            'pair_info': {
                'target_key': sample['target_key'],
                'context_key': sample['context_key'],
                'pair_type': sample['pair_type']
            }
        }


class Stage2Trainer:
    """Trainer for Stage 2 context-enhanced model"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize model
        self._setup_model()
        
        # Setup loss and optimizer
        self._setup_training()
        
        # Tracking
        self.best_auc = 0.0
        self.epoch = 0
        
    def _setup_model(self):
        """Setup the EPT+Context wrapper model"""
        # Load EPT configuration
        ept_config = self.config['ept_model']
        context_config = self.config.get('context_model', {})
        
        # Multi-stage training support
        freeze_stage1 = self.config['training'].get('freeze_stage1', True)
        
        # Create wrapper model
        self.model = EPTContextWrapper(
            ept_config=ept_config,
            context_config=context_config,
            freeze_stage1=freeze_stage1,
            use_stage2=True
        ).to(self.device)
        
        # Load Stage 1 checkpoint
        stage1_ckpt = self.config['stage1_checkpoint']
        self.model.load_stage1_checkpoint(stage1_ckpt)
        
        print_log(f"Loaded Stage 1 checkpoint: {stage1_ckpt}")
        print_log(f"Stage 1 frozen: {freeze_stage1}")
        
    def _setup_training(self):
        """Setup loss function and optimizer with multi-stage support"""
        # Loss function with class weighting
        pos_weight = self.config['training'].get('pos_weight', None)
        if pos_weight:
            pos_weight = torch.tensor(pos_weight).to(self.device)
            
        self.criterion = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
        
        # Multi-stage optimization setup
        freeze_stage1 = self.config['training'].get('freeze_stage1', True)
        stage1_lr_ratio = self.config['training'].get('stage1_lr_ratio', 0.1)
        base_lr = self.config['training']['learning_rate']
        
        if freeze_stage1:
            # Stage 2 only training
            trainable_params = self.model.get_trainable_parameters('stage2')
            self.optimizer = optim.Adam(
                trainable_params,
                lr=base_lr,
                weight_decay=self.config['training']['weight_decay']
            )
            print_log(f"Training Stage 2 only: {sum(p.numel() for p in trainable_params)} parameters")
        else:
            # Joint training with different learning rates
            stage1_params = self.model.get_trainable_parameters('stage1')
            stage2_params = self.model.get_trainable_parameters('stage2')
            
            self.optimizer = optim.Adam([
                {'params': stage1_params, 'lr': base_lr * stage1_lr_ratio},
                {'params': stage2_params, 'lr': base_lr}
            ], weight_decay=self.config['training']['weight_decay'])
            
            print_log(f"Joint training - Stage 1: {sum(p.numel() for p in stage1_params)} params (LR: {base_lr * stage1_lr_ratio})")
            print_log(f"Joint training - Stage 2: {sum(p.numel() for p in stage2_params)} params (LR: {base_lr})")
        
        # Learning rate scheduler
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='max',
            factor=self.config['training']['lr_decay_factor'],
            patience=self.config['training']['lr_patience'],
        )
        
        # Warmup configuration
        self.warmup_epochs = self.config['training'].get('warmup_stage2_only', 0)
        self.current_training_mode = 'stage2_only' if self.warmup_epochs > 0 else 'joint_training'
        
        if self.warmup_epochs > 0:
            print_log(f"Warmup: Stage 2 only for first {self.warmup_epochs} epochs")
        
    def setup_data(self):
        """Setup training and validation datasets"""
        # Get training phase from config
        training_phase = 'stage2_only' if self.config['training'].get('freeze_stage1', True) else 'joint_training'
        
        # Create datasets using stage2 data format
        train_dataset = Stage2ContextDataset(
            stage2_dataset_path=self.config['data']['train_path'],
            context_config=self.config['data']['context_config'],
            mode='train',
            training_phase=training_phase
        )
        
        val_dataset = Stage2ContextDataset(
            stage2_dataset_path=self.config['data']['val_path'],
            context_config=self.config['data']['context_config'],
            mode='val',
            training_phase=training_phase
        )
        
        # Store datasets for potential phase switching
        self.train_dataset = train_dataset
        self.val_dataset = val_dataset
        
        # Context data is now directly embedded in each sample - no registration needed
        
        # Create target-aware batch samplers
        train_batch_sampler = TargetGroupedBatchSampler(
            dataset=train_dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=True,
            drop_last=self.config['training'].get('drop_last', False),
            min_samples_per_target=self.config['training'].get('min_samples_per_target', 1)
        )
        
        val_batch_sampler = TargetGroupedBatchSampler(
            dataset=val_dataset,
            batch_size=self.config['training']['val_batch_size'],
            shuffle=False,
            drop_last=False,
            min_samples_per_target=1
        )
        
        # Create data loaders with target-aware sampling
        self.train_loader = DataLoader(
            train_dataset,
            batch_sampler=train_batch_sampler,
            num_workers=self.config['data']['num_workers'],
            collate_fn=self._collate_fn_target_aware
        )
        
        self.val_loader = DataLoader(
            val_dataset,
            batch_sampler=val_batch_sampler,
            num_workers=self.config['data']['num_workers'],
            collate_fn=self._collate_fn_target_aware
        )
        
        
    def _collate_fn_target_aware(self, batch):
        """
        Target-aware collate function for homogeneous target batches
        
        Since TargetGroupedBatchSampler ensures all samples in a batch belong to
        the same target, we can process them more efficiently.
        """
        if not batch:
            return None
            
        # All samples should have the same target_id due to our sampler
        target_id = batch[0]['target_id']
        
        # Verify all samples are from the same target (debug check)
        assert all(sample['target_id'] == target_id for sample in batch), \
            f"Mixed targets in batch: {set(sample['target_id'] for sample in batch)}"
        
        # Prepare query batch
        query_data_list = [sample['query_data'] for sample in batch]
        query_batch = self._prepare_query_batch(query_data_list)
        
        # Prepare context batch (all samples should have same context since they're from same target)
        context_data_list = [sample['context_data'] for sample in batch]
        context_labels_list = [sample['context_labels'] for sample in batch]
        
        # Extract labels
        labels = [sample['query_data']['label'][0] for sample in batch]
        
        return {
            'target_id': target_id,
            'query_batch': query_batch,
            'context_data': context_data_list,
            'context_labels': context_labels_list,
            'labels': labels,
            'batch_size': len(batch)
        }
        
    def _collate_fn(self, batch):
        """Legacy collate function for mixed target batches (fallback)"""
        # Group by target for efficient processing
        target_groups = defaultdict(list)
        for sample in batch:
            target_groups[sample['target_id']].append(sample)
            
        return target_groups
        
    def train_epoch(self):
        """Train for one epoch with target-aware batching"""
        self.model.set_stage1_mode('eval')  # Keep Stage 1 frozen
        self.model.set_stage2_mode('train')
        
        total_loss = 0.0
        all_preds = []
        all_labels = []
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {self.epoch}')
        for batch_idx, batch_data in enumerate(pbar):
            if batch_data is None:
                continue
                
            # Extract batch information
            target_id = batch_data['target_id']
            query_batch = batch_data['query_batch']
            context_data = batch_data['context_data']
            context_labels = batch_data['context_labels']
            labels = torch.tensor(batch_data['labels'], dtype=torch.float).to(self.device)
            
            # Move query batch to device
            for key in query_batch:
                if isinstance(query_batch[key], torch.Tensor):
                    query_batch[key] = query_batch[key].to(self.device)
            
            # Forward pass with context data directly
            ret = self.model(
                target_id=target_id,
                context_data=context_data,
                context_labels=context_labels,
                **query_batch
            )
            
            # Use Stage 2 predictions if available, else Stage 1
            if ret.stage2_logits is not None:
                logits = ret.stage2_logits.squeeze(-1)
            else:
                logits = ret.stage1_logits.squeeze(-1)
                
            # Compute loss
            loss = self.criterion(logits, labels)
            
            # Backward pass
            self.optimizer.zero_grad()
            loss.backward()
            
            # Gradient clipping
            if self.config['training'].get('grad_clip', 0) > 0:
                trainable_params = self.model.get_trainable_parameters('stage2')
                torch.nn.utils.clip_grad_norm_(trainable_params, 
                                             self.config['training']['grad_clip'])
            
            self.optimizer.step()
            total_loss += loss.item()
            
            # Track predictions
            preds = torch.sigmoid(logits).detach().cpu().numpy()
            all_preds.extend(preds)
            all_labels.extend(labels.cpu().numpy())
            
            # Update progress
            pbar.set_postfix({'loss': f'{loss.item():.4f}', 'target': target_id})
            
        # Compute metrics
        avg_loss = total_loss / len(self.train_loader)
        auc = roc_auc_score(all_labels, all_preds) if len(set(all_labels)) > 1 else 0.0
        ap = average_precision_score(all_labels, all_preds) if len(set(all_labels)) > 1 else 0.0
        
        print_log(f'Epoch {self.epoch} Train - Loss: {avg_loss:.4f}, AUC: {auc:.4f}, AP: {ap:.4f}')
        return avg_loss, auc, ap
        
    def validate(self):
        """Validate the model with target-aware batching"""
        self.model.set_stage1_mode('eval')
        self.model.set_stage2_mode('eval')
        
        total_loss = 0.0
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for batch_data in tqdm(self.val_loader, desc='Validation'):
                if batch_data is None:
                    continue
                    
                # Extract batch information
                target_id = batch_data['target_id']
                query_batch = batch_data['query_batch']
                context_data = batch_data['context_data']
                context_labels = batch_data['context_labels']
                labels = torch.tensor(batch_data['labels'], dtype=torch.float).to(self.device)
                
                # Move to device
                for key in query_batch:
                    if isinstance(query_batch[key], torch.Tensor):
                        query_batch[key] = query_batch[key].to(self.device)
                
                # Forward pass with context data directly
                ret = self.model(
                    target_id=target_id,
                    context_data=context_data,
                    context_labels=context_labels,
                    **query_batch
                )
                
                if ret.stage2_logits is not None:
                    logits = ret.stage2_logits.squeeze(-1)
                else:
                    logits = ret.stage1_logits.squeeze(-1)
                    
                loss = self.criterion(logits, labels)
                total_loss += loss.item()
                
                preds = torch.sigmoid(logits).cpu().numpy()
                all_preds.extend(preds)
                all_labels.extend(labels.cpu().numpy())
                    
        # Compute metrics
        avg_loss = total_loss / len(self.val_loader)
        auc = roc_auc_score(all_labels, all_preds) if len(set(all_labels)) > 1 else 0.0
        ap = average_precision_score(all_labels, all_preds) if len(set(all_labels)) > 1 else 0.0
        
        print_log(f'Epoch {self.epoch} Val - Loss: {avg_loss:.4f}, AUC: {auc:.4f}, AP: {ap:.4f}')
        return avg_loss, auc, ap
        
    def _prepare_query_batch(self, query_data_list):
        """Prepare batch from list of query data with proper field mapping"""
        # Use CLSDataset's collate function first
        batch = CLSDataset.collate_fn(query_data_list)

        # Define the mapping from expected model args to keys in the data file
        # Based on PredictorNNNModel.forward signature: Z, B, A, atom_positions, block_lengths, lengths, segment_ids
        key_map = {
            'Z': 'X',  # EPT model expects 'Z' for coordinates, data has 'X'
            'B': 'B',  # Bond types
            'A': 'A',  # Atomic numbers
            'atom_positions': 'atom_positions',
            'block_lengths': 'block_lengths',
            'lengths': 'lengths',
            'segment_ids': 'segment_ids'
        }

        # Create filtered batch with only expected fields and proper mapping
        filtered_batch = {}
        for model_arg, data_key in key_map.items():
            if data_key in batch:
                filtered_batch[model_arg] = batch[data_key]

        return filtered_batch
        
    def save_checkpoint(self, is_best: bool = False):
        """Save model checkpoint"""
        checkpoint_dir = self.config['output']['save_dir']
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Regular checkpoint
        checkpoint_path = os.path.join(checkpoint_dir, f'stage2_epoch_{self.epoch}.pth')
        self.model.save_checkpoint(
            path=checkpoint_path,
            epoch=self.epoch,
            optimizer_state=self.optimizer.state_dict()
        )
        
        # Best model
        if is_best:
            best_path = os.path.join(checkpoint_dir, 'stage2_best.pth')
            self.model.save_checkpoint(
                path=best_path,
                epoch=self.epoch,
                optimizer_state=self.optimizer.state_dict()
            )
            print_log(f'New best model saved: {best_path}')
            
    def train(self):
        """Main training loop"""
        print_log("Starting Stage 2 training...")
        
        # Setup data
        self.setup_data()
        
        # Training loop
        for epoch in range(1, self.config['training']['epochs'] + 1):
            self.epoch = epoch
            
            # Train and validate
            train_loss, train_auc, train_ap = self.train_epoch()
            val_loss, val_auc, val_ap = self.validate()
            
            # Learning rate scheduling
            self.scheduler.step(val_auc)
            
            # Save checkpoint
            is_best = val_auc > self.best_auc
            if is_best:
                self.best_auc = val_auc
                
            if epoch % self.config['output']['save_freq'] == 0 or is_best:
                self.save_checkpoint(is_best)
                
        print_log(f'Training completed! Best AUC: {self.best_auc:.4f}')


def parse_args():
    parser = argparse.ArgumentParser(description='Stage 2 Context-Aware Training')
    parser.add_argument('--config', type=str, required=False, help='Config file path',default='/home/<USER>/colossus_code/EPT/stage2/stage2_config.yaml')
    parser.add_argument('--stage1_ckpt', type=str, required=False, help='Stage 1 checkpoint',default='/home/<USER>/colossus_code/EPT/ckpts/version_0/checkpoint/epoch1_step128004.ckpt')
    parser.add_argument('--output_dir', type=str, help='Override output directory',default='/home/<USER>/colossus_code/EPT/stage2/stage2_data_blocks_fixed')
    return parser.parse_args()


def main():
    args = parse_args()
    
    # Load config
    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)
        
    # Override with command line args
    config['stage1_checkpoint'] = args.stage1_ckpt
    
    if args.output_dir:
        config['output']['save_dir'] = args.output_dir
        
    # Setup logging
    setup_logging(config['output']['save_dir'])
    
    # Train
    trainer = Stage2Trainer(config)
    trainer.train()


if __name__ == '__main__':
    main()
