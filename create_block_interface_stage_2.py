#!/usr/bin/python
# -*- coding:utf-8 -*-
import os
import sys
# 获取当前脚本的绝对路径的上上级目录：/home/<USER>/titan_code/EPT-F3D1/
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))

# 添加到 sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import argparse
import numpy as np
from tqdm import tqdm
from multiprocessing import Pool
import warnings
import pickle
import pandas as pd
from utils.logger import print_log
from data.converter.blocks_to_data import blocks_to_data
from data.converter.mol2_to_blocks import mol2_to_blocks
from data.converter.pdb_to_list_blocks import pdb_to_list_blocks
from data.mmap_dataset import create_mmap
from rdkit import Chem
# Find interface residues using blocks_interface
from data.converter.blocks_interface import blocks_interface
from collections import defaultdict
import random

remove_list_uniprot = ['P04183', 'P05979', 'P08253', 'P08684', 'P11509', 'P11712', 'P15121', 'P23526', 'P28845', 'P29476', 'P35557', 'P80276', 'P9WGR1', 'Q02127', 'Q15788', 'Q9BYF1', 'Q9PPP5'] 

def parse():
    parser = argparse.ArgumentParser(description='Block creation for stage 2 training with context enrichment using hiqbind data')
    parser.add_argument('--csv_file', type=str, required=False, help='CSV file with diverse multi-binder entries', default='/home/<USER>/colossus_code/diverse_multi_binder_entries.csv')
    
    parser.add_argument('--protein_dir', type=str, required=False, help='Directory with protein PDB files', default='/home/<USER>/colossus_code/stage2_data/hiqbind_multibinders')

    parser.add_argument('--ligand_repo', type=str, required=False, help='Directory with good ligand poses (active poses, RMSD < 2)', default='/home/<USER>/colossus_code/stage2_data/rmsd_analysis/active_mol2')
    
    parser.add_argument('--decoy_repo', type=str, required=False, help='Directory with bad ligand poses (decoy poses, RMSD > 4)', default='/home/<USER>/colossus_code/stage2_data/rmsd_analysis/decoy_mol2')

    parser.add_argument('--out_dir', type=str, default='/home/<USER>/colossus_code/EPT/stage2_data_blocks_fixed', help='Output directory for mmap storage')
    parser.add_argument('--encoding', type=str, default='complex', choices=['complex', 'separate'], help='Encoding scenario: complex (joint protein+ligand), separate (store protein and ligand blocks separately)')

    parser.add_argument('--num_workers', type=int, default=os.cpu_count()-1, help='Number of processes for parallel block creation')
    
    parser.add_argument('--interface_dist', type=float, default=8.0, help='Distance threshold (Å) for interface detection')
    parser.add_argument('--max_poses_per_target', type=int, default=100, help='Maximum number of docked poses to select per target')
    parser.add_argument('--train_split', type=float, default=0.8, help='Fraction of data for training')
    parser.add_argument('--random_seed', type=int, default=42, help='Random seed for reproducible sampling and splitting')
    return parser.parse_args()


def load_context_groups(csv_file):
    """
    Load CSV file and group by UniProt, determining context target (lowest resolution) for each group.
    Returns: dict mapping {uniprot_id: {'context': context_entry, 'targets': [other_entries]}}
    """
    df = pd.read_csv(csv_file)
    print_log(f"Loaded {len(df)} entries from {csv_file}")
    
    groups = {}
    for uniprot_id, group in df.groupby('UniProt'):
        # Sort by resolution to find the context (lowest resolution = best quality)
        sorted_group = group.sort_values('Resolution')
        context_entry = sorted_group.iloc[0].to_dict()
        target_entries = [sorted_group.iloc[i].to_dict() for i in range(1, len(sorted_group))]
        
        if len(target_entries) > 0:  # Only include groups with multiple entries
            groups[uniprot_id] = {
                'context': context_entry,
                'targets': target_entries
            }
            print_log(f"UniProt {uniprot_id}: context {context_entry['PDBID']} (res: {context_entry['Resolution']}) -> {len(target_entries)} targets", level='DEBUG')
    
    print_log(f"Created {len(groups)} context groups from {df['UniProt'].nunique()} unique UniProt entries")
    return groups

def build_protein_index(protein_dir):
    """
    Builds a dict mapping protein IDs to their PDB file paths for hiqbind data.
    """
    index = {}
    if not os.path.isdir(protein_dir):
        print_log(f"Protein directory {protein_dir} not found.", level='ERROR')
        return index
        
    for pdb_subdir in os.listdir(protein_dir):
        subdir_path = os.path.join(protein_dir, pdb_subdir)
        if os.path.isdir(subdir_path):
            # Look for protein PDB file in subdirectory
            for fname in os.listdir(subdir_path):
                if fname.endswith('_protein_refined.pdb'):
                    full_path = os.path.join(subdir_path, fname)
                    # Extract target name from filename (e.g., "10gs_VWW_B_protein_refined.pdb" -> "10gs_VWW_B")
                    target_name = fname.replace('_protein_refined.pdb', '')
                    index[target_name] = full_path
                    print_log(f"Mapped protein: {target_name} -> {fname}", level='DEBUG')
    
    print_log(f"Built protein index with {len(index)} entries", level='INFO')
    return index

def find_matching_protein(target_name, protein_index):
    """
    Find the matching protein file for a given target name.
    """
    if target_name in protein_index:
        return protein_index[target_name]
    
    available_targets = list(protein_index.keys())
    print_log(f"No exact match for target '{target_name}'. Available targets: {available_targets[:10]}...", level='DEBUG')
    
    return None

def load_ligand_blocks(ligand_path):
    """Load ligand blocks from mol2 files"""
    if ligand_path.endswith('.mol2'):
        return mol2_to_blocks(ligand_path)
    else:
        raise ValueError(f'Unsupported ligand file format for block conversion: {ligand_path}. Only .mol2 is supported.')

def create_entry_key(entry):
    """Create a unique key for an entry from CSV data"""
    return f"{entry['PDBID']}_{entry['Ligand Name']}_{entry['Ligand Chain']}"

def collect_context_target_pairs(context_groups, ligand_repo, decoy_repo, max_poses_per_target=10, random_seed=42):
    """
    Collect context-target pairs for stage 2 training.
    
    Args:
        context_groups: Dict from load_context_groups
        ligand_repo: Directory with good ligand poses (active_mol2)
        decoy_repo: Directory with bad ligand poses (decoy_mol2)
        max_poses_per_target: Maximum poses per target
        random_seed: Random seed for sampling
    
    Returns:
        List of (context_ligand_path, context_key, target_ligand_path, target_key, label, pair_type, uniprot_id) tuples
    """
    random.seed(random_seed)
    pairs = []
    
    # Track removed UniProt groups for reporting
    removed_uniprot_groups = []
    
    for uniprot_id, group_data in context_groups.items():
        # Skip if UniProt ID is in the exclusion list
        if uniprot_id in remove_list_uniprot:
            context_entry = group_data['context']
            target_entries = group_data['targets']
            removed_uniprot_groups.append({
                'uniprot_id': uniprot_id,
                'context_entry': {
                    'key': create_entry_key(context_entry),
                    'pdb_id': context_entry['PDBID'],
                    'resolution': context_entry['Resolution']
                },
                'target_entries': [
                    {
                        'key': create_entry_key(target),
                        'pdb_id': target['PDBID'],
                        'resolution': target['Resolution']
                    }
                    for target in target_entries
                ],
                'total_entries': len(target_entries) + 1,
                'reason': 'UniProt ID in remove_list_uniprot'
            })
            print_log(f"Skipping UniProt group {uniprot_id} - UniProt ID in remove_list_uniprot", level='DEBUG')
            continue
        
        context_entry = group_data['context']
        target_entries = group_data['targets']
        context_key = create_entry_key(context_entry)
        
        # Find context ligand files (good poses from active_mol2)
        context_active_dir = os.path.join(ligand_repo, context_key)
        context_ligand_files = []
        if os.path.isdir(context_active_dir):
            for fname in os.listdir(context_active_dir):
                if fname.endswith('.mol2'):
                    context_ligand_files.append(os.path.join(context_active_dir, fname))
        
        if not context_ligand_files:
            print_log(f"No context ligand files found for {context_key}", level='WARN')
            continue
        
        # Use the first active pose as context (or could sample randomly)
        context_ligand_path = context_ligand_files[0]
            
        # For each target in this group (no individual filtering needed since group-level filtering already done)
        for target_entry in target_entries:
            target_key = create_entry_key(target_entry)
            
            # Find target active poses (good poses - label=1)
            target_active_dir = os.path.join(ligand_repo, target_key)
            if os.path.isdir(target_active_dir):
                target_active_files = []
                for fname in os.listdir(target_active_dir):
                    if fname.endswith('.mol2'):
                        target_active_files.append(os.path.join(target_active_dir, fname))
                
                # Sample up to max_poses_per_target active poses
                if target_active_files:
                    num_to_select = min(max_poses_per_target, len(target_active_files))
                    selected_active = random.sample(target_active_files, num_to_select)
                    
                    for active_path in selected_active:
                        pairs.append((
                            context_ligand_path, context_key,
                            active_path, target_key,
                            1, 'context_vs_active', uniprot_id
                        ))
            
            # Find target decoy poses (bad poses - label=0)
            target_decoy_dir = os.path.join(decoy_repo, target_key)
            if os.path.isdir(target_decoy_dir):
                target_decoy_files = []
                for fname in os.listdir(target_decoy_dir):
                    if fname.endswith('.mol2'):
                        target_decoy_files.append(os.path.join(target_decoy_dir, fname))
                
                # Sample up to max_poses_per_target decoy poses
                if target_decoy_files:
                    num_to_select = min(max_poses_per_target, len(target_decoy_files))
                    selected_decoy = random.sample(target_decoy_files, num_to_select)
                    
                    for decoy_path in selected_decoy:
                        pairs.append((
                            context_ligand_path, context_key,
                            decoy_path, target_key,
                            0, 'context_vs_decoy', uniprot_id
                        ))
    
    # Report removed UniProt groups
    if removed_uniprot_groups:
        print_log(f"REMOVED UNIPROT GROUPS ({len(removed_uniprot_groups)} total):")
        total_removed_entries = 0
        
        for group in removed_uniprot_groups:
            uniprot_id = group['uniprot_id']
            total_entries = group['total_entries']
            total_removed_entries += total_entries
            
            print_log(f"  - UniProt {uniprot_id}: {total_entries} entries removed (reason: {group['reason']})")
            print_log(f"    Context: {group['context_entry']['key']} (PDB: {group['context_entry']['pdb_id']}, Res: {group['context_entry']['resolution']}Å)")
            
            for target in group['target_entries']:
                print_log(f"    Target: {target['key']} (PDB: {target['pdb_id']}, Res: {target['resolution']}Å)")
        
        print_log(f"SUMMARY: Removed {len(removed_uniprot_groups)} UniProt groups containing {total_removed_entries} total entries due to remove_list_uniprot filtering")
    else:
        print_log("No UniProt groups were removed due to remove_list_uniprot filtering")
    
    remaining_groups = len(context_groups) - len(removed_uniprot_groups)
    print_log(f"Created {len(pairs)} context-target pairs from {remaining_groups} remaining UniProt groups (out of {len(context_groups)} original groups)")
    return pairs

def process_context_target_pair(args):
    """Process a context-target pair for stage 2 training"""
    try:
        (context_ligand_path, context_key, target_ligand_path, target_key, 
         label, pair_type, uniprot_id, protein_index, encoding, interface_dist) = args
        
        # Load context ligand and find its protein
        context_ligand_blocks = load_ligand_blocks(context_ligand_path)
        context_protein_path = protein_index.get(context_key)
        if not context_protein_path or not os.path.exists(context_protein_path):
            print_log(f"Context protein not found for {context_key}")
            return None
            
        # Load target ligand and find its protein  
        target_ligand_blocks = load_ligand_blocks(target_ligand_path)
        target_protein_path = protein_index.get(target_key)
        if not target_protein_path or not os.path.exists(target_protein_path):
            print_log(f"Target protein not found for {target_key}")
            return None
        
        # Load protein structures
        context_protein_blocks = pdb_to_list_blocks(context_protein_path)
        target_protein_blocks = pdb_to_list_blocks(target_protein_path)
        
        # Convert protein blocks to flat lists
        if isinstance(context_protein_blocks, list) and len(context_protein_blocks) > 0 and isinstance(context_protein_blocks[0], list):
            flat_context_protein_blocks = [blk for chain in context_protein_blocks for blk in chain]
        else:
            flat_context_protein_blocks = context_protein_blocks
            
        if isinstance(target_protein_blocks, list) and len(target_protein_blocks) > 0 and isinstance(target_protein_blocks[0], list):
            flat_target_protein_blocks = [blk for chain in target_protein_blocks for blk in chain]
        else:
            flat_target_protein_blocks = target_protein_blocks
        
        # Create interfaces
        context_interface_blocks, _ = blocks_interface(flat_context_protein_blocks, context_ligand_blocks, interface_dist)
        target_interface_blocks, _ = blocks_interface(flat_target_protein_blocks, target_ligand_blocks, interface_dist)
        
        if not context_interface_blocks or not target_interface_blocks:
            print_log(f"No interface found for context {context_key} or target {target_key}")
            return None
        
        # Create unique pair ID
        pair_id = f"{uniprot_id}__{context_key}_to_{target_key}_{pair_type}"
        
        # For stage 2, we need both context and target information
        if encoding == 'complex':
            # Create context data, ensuring all necessary keys are preserved
            context_data_raw = blocks_to_data(context_interface_blocks, context_ligand_blocks)
            context_data = {k: v.tolist() if isinstance(v, np.ndarray) else v for k, v in context_data_raw.items()}

            # Create target data, ensuring all necessary keys are preserved
            target_data_raw = blocks_to_data(target_interface_blocks, target_ligand_blocks)
            target_data = {k: v.tolist() if isinstance(v, np.ndarray) else v for k, v in target_data_raw.items()}
            
            # Add coordinates (C) for compatibility with the collate function, which expects 'C'
            target_data['C'] = target_data.get('X')
            context_data['C'] = context_data.get('X')

            # Combine into stage 2 format
            stage2_data = {
                'pair_id': pair_id,
                'uniprot_id': uniprot_id,
                'context_key': context_key,
                'target_key': target_key,
                'pair_type': pair_type,
                'label': label,
                'context_data': context_data,
                'target_data': target_data
            }
            
            return pair_id, stage2_data
        else:
            raise ValueError(f"Only 'complex' encoding supported for stage 2, got: {encoding}")

    except Exception as e:
        print_log(f"Failed to process context-target pair {context_key}->{target_key}: {e}")
        return None


def main(args):
    os.makedirs(args.out_dir, exist_ok=True)
    
    # Load context groups from CSV
    print_log("Loading context groups from CSV...")
    context_groups = load_context_groups(args.csv_file)
    if not context_groups:
        print_log("No context groups found. Exiting.", level='ERROR')
        return
    
    # Build protein index
    print_log("Building protein index...")
    protein_index = build_protein_index(args.protein_dir)
    if not protein_index:
        print_log("No protein files found. Exiting.", level='ERROR')
        return
    
    # Collect context-target pairs
    print_log("Collecting context-target pairs...")
    all_pairs = collect_context_target_pairs(
        context_groups, 
        args.ligand_repo, 
        args.decoy_repo, 
        args.max_poses_per_target, 
        args.random_seed
    )
    
    if not all_pairs:
        print_log("No context-target pairs found. Exiting.", level='ERROR')
        return
    
    # Split data into train/val
    random.seed(args.random_seed)
    random.shuffle(all_pairs)
    
    train_size = int(len(all_pairs) * args.train_split)
    train_pairs = all_pairs[:train_size]
    val_pairs = all_pairs[train_size:]
    
    print_log(f"Split {len(all_pairs)} pairs into {len(train_pairs)} train and {len(val_pairs)} val")
    
    # Process and save data for each split
    def process_and_save_stage2(pairs, split_name, out_path):
        if not pairs:
            print_log(f"No pairs to process for {split_name} split.")
            return
            
        results = []
        pair_index = []
        
        print_log(f"Processing {len(pairs)} context-target pairs for {split_name} split...")
        
        # Prepare arguments for multiprocessing
        process_args = []
        for pair in pairs:
            (context_ligand_path, context_key, target_ligand_path, target_key, 
             label, pair_type, uniprot_id) = pair
            
            process_args.append((
                context_ligand_path, context_key, target_ligand_path, target_key,
                label, pair_type, uniprot_id, protein_index, args.encoding, args.interface_dist
            ))
        
        with Pool(processes=args.num_workers) as pool:
            for res in tqdm(pool.imap_unordered(process_context_target_pair, process_args), 
                          total=len(process_args), desc=f"Processing {split_name} pairs"):
                if res is None:
                    continue
                try:
                    pair_id, data = res
                    results.append((pair_id, data))
                    pair_index.append({
                        'pair_id': pair_id,
                        'uniprot_id': data.get('uniprot_id', ''),
                        'context_key': data.get('context_key', ''),
                        'target_key': data.get('target_key', ''),
                        'pair_type': data.get('pair_type', ''),
                        'label': data.get('label', '')
                    })
                except Exception as e:
                    print_log(f"Failed to process result for {split_name}: {e}", level='ERROR')
        
        if not results:
            print_log(f"No results were successfully processed for {split_name}.", level='WARN')
            return
        
        # Save data
        import csv
        print_log(f"Storing {len(results)} processed stage2 items for {split_name} to {out_path}")
        
        def result_iter():
            for pair_id_item, data_item in results:
                yield pair_id_item, data_item, []
        
        try:
            create_mmap(result_iter(), out_path, len(results))
        except Exception as e:
            print_log(f"Error creating mmap dataset for {split_name} at {out_path}: {e}", level='ERROR')
            return
        
        # Save pair index
        if pair_index:
            pair_index_path = os.path.join(out_path, 'pair_index.csv')
            try:
                with open(pair_index_path, 'w', newline='') as csvfile:
                    fieldnames = ['pair_id', 'uniprot_id', 'context_key', 'target_key', 'pair_type', 'label']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    for row in pair_index:
                        writer.writerow(row)
                print_log(f"Stage2 pair index for {split_name} saved to {pair_index_path}")
            except Exception as e:
                print_log(f"Error saving pair index CSV for {split_name} at {pair_index_path}: {e}", level='ERROR')
    
    # Process and save train and val splits
    train_out_path = os.path.join(args.out_dir, 'train')
    val_out_path = os.path.join(args.out_dir, 'val')
    
    # process_and_save_stage2(train_pairs, "Train", train_out_path)
    process_and_save_stage2(val_pairs, "Val", val_out_path)
    
    print_log('Stage 2 data processing finished!')

if __name__ == '__main__':
    main(parse())