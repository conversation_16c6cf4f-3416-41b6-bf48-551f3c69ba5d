#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
Context-Aware Module for Ligand Screening

This module implements a context-aware approach for virtual screening:
1. Context Bank: Stores known active/inactive ligands as a "knowledge base"
2. Retrieval: Finds most relevant known ligands for each query
3. Reasoning: Aggregates evidence from retrieved ligands to enhance predictions

Logic: The module retrieves relevant known ligands to predict new ligand activity.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Union
import numpy as np
from dataclasses import dataclass
import logging


@dataclass
class LigandContext:
    """Represents a known ligand in the context bank"""
    embedding: torch.Tensor  # [hidden_size]
    label: int              # 0 or 1 (inactive/active)
    confidence: float       # Optional confidence score
    metadata: Dict          # Additional info (e.g., IC50, structure info)


class AdaptiveContextBank:
    """
    Context bank that stores and retrieves relevant ligands
    
    Key Features:
    - Handles any ratio of active/inactive ligands
    - Adaptive retrieval based on query similarity
    - Importance weighting for informative examples
    """
    
    def __init__(self, 
                 max_context_size: int = 100,
                 min_actives: int = 1,
                 min_inactives: int = 1,
                 diversity_threshold: float = 0.8):
        """
        Args:
            max_context_size: Maximum number of ligands to store per target
            min_actives/min_inactives: Minimum required examples of each class
            diversity_threshold: Cosine similarity threshold for diversity filtering
        """
        self.max_context_size = max_context_size
        self.min_actives = min_actives
        self.min_inactives = min_inactives
        self.diversity_threshold = diversity_threshold
        
        # Storage for different targets
        self.target_contexts: Dict[str, List[LigandContext]] = {}
        self.target_embeddings: Dict[str, torch.Tensor] = {}
        self.target_labels: Dict[str, torch.Tensor] = {}
        
    def add_target_context(self, 
                          target_id: str, 
                          embeddings: torch.Tensor,
                          labels: torch.Tensor,
                          confidences: Optional[torch.Tensor] = None,
                          metadata: Optional[List[Dict]] = None):
        """
        Add context ligands for a specific target with adaptive curation
        
        Args:
            target_id: Unique identifier for the target
            embeddings: [N, hidden_size] - ligand embeddings
            labels: [N] - binary labels (0/1)
            confidences: [N] - optional confidence scores
            metadata: List of metadata dicts
        """
        n_ligands = embeddings.shape[0]
        
        if confidences is None:
            confidences = torch.ones(n_ligands)
        if metadata is None:
            metadata = [{} for _ in range(n_ligands)]
            
        # Create LigandContext objects
        contexts = []
        for i in range(n_ligands):
            context = LigandContext(
                embedding=embeddings[i],
                label=labels[i].item(),
                confidence=confidences[i].item(),
                metadata=metadata[i]
            )
            contexts.append(context)
        
        # Apply adaptive curation
        curated_contexts = self._curate_contexts(contexts)
        
        # Store curated contexts
        self.target_contexts[target_id] = curated_contexts
        
        # Pre-compute tensors for efficient retrieval
        self._precompute_target_tensors(target_id)
        
        logging.info(f"Added {len(curated_contexts)} curated contexts for target {target_id}")
        
    def _curate_contexts(self, contexts: List[LigandContext]) -> List[LigandContext]:
        """
        Intelligent curation of context ligands
        
        Strategy:
        1. Ensure minimum representation of both classes
        2. Prioritize high-confidence examples
        3. Maintain diversity to avoid redundancy
        4. Balance classes if needed
        """
        # Separate by class
        actives = [c for c in contexts if c.label == 1]
        inactives = [c for c in contexts if c.label == 0]
        
        # Sort by confidence (higher confidence first)
        actives.sort(key=lambda x: x.confidence, reverse=True)
        inactives.sort(key=lambda x: x.confidence, reverse=True)
        
        # Apply diversity filtering within each class
        curated_actives = self._apply_diversity_filter(actives)
        curated_inactives = self._apply_diversity_filter(inactives)
        
        # Ensure minimum representation
        if len(curated_actives) < self.min_actives and len(actives) >= self.min_actives:
            curated_actives = actives[:self.min_actives]
        if len(curated_inactives) < self.min_inactives and len(inactives) >= self.min_inactives:
            curated_inactives = inactives[:self.min_inactives]
            
        # Adaptive balancing based on available data
        total_budget = self.max_context_size
        active_budget = min(len(curated_actives), total_budget // 2)
        inactive_budget = min(len(curated_inactives), total_budget - active_budget)
        
        # If one class has fewer examples, give more budget to the other
        if len(curated_actives) < active_budget:
            inactive_budget = min(len(curated_inactives), total_budget - len(curated_actives))
            active_budget = len(curated_actives)
        elif len(curated_inactives) < inactive_budget:
            active_budget = min(len(curated_actives), total_budget - len(curated_inactives))
            inactive_budget = len(curated_inactives)
            
        # Final selection
        selected_actives = curated_actives[:active_budget]
        selected_inactives = curated_inactives[:inactive_budget]
        
        curated = selected_actives + selected_inactives
        
        logging.info(f"Curated to {len(selected_actives)} actives, {len(selected_inactives)} inactives")
        
        return curated
    
    def _apply_diversity_filter(self, contexts: List[LigandContext]) -> List[LigandContext]:
        """Apply diversity filtering to avoid redundant similar ligands"""
        if len(contexts) <= 1:
            return contexts
            
        selected = [contexts[0]]  # Always include highest confidence
        
        for candidate in contexts[1:]:
            # Check similarity with already selected
            is_diverse = True
            for selected_context in selected:
                similarity = F.cosine_similarity(
                    candidate.embedding.unsqueeze(0),
                    selected_context.embedding.unsqueeze(0)
                ).item()
                
                if similarity > self.diversity_threshold:
                    is_diverse = False
                    break
                    
            if is_diverse:
                selected.append(candidate)
                
        return selected
    
    def _precompute_target_tensors(self, target_id: str):
        """Pre-compute tensors for efficient similarity computation"""
        contexts = self.target_contexts[target_id]
        
        embeddings = torch.stack([c.embedding for c in contexts])  # [N, hidden_size]
        labels = torch.tensor([c.label for c in contexts], dtype=torch.float)  # [N]
        
        self.target_embeddings[target_id] = embeddings
        self.target_labels[target_id] = labels
    
    def retrieve_relevant_contexts(self, 
                                 target_id: str,
                                 query_embedding: torch.Tensor,
                                 top_k: int = 10,
                                 temperature: float = 0.1) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Retrieval of most relevant context ligands
        
        Args:
            target_id: Target identifier
            query_embedding: [hidden_size] - query ligand embedding
            top_k: Number of most similar contexts to retrieve
            temperature: Temperature for similarity softmax
            
        Returns:
            retrieved_embeddings: [top_k, hidden_size]
            retrieved_labels: [top_k]
            similarity_scores: [top_k] - retrieval confidence scores
        """
        if target_id not in self.target_embeddings:
            raise ValueError(f"No context available for target: {target_id}")
            
        context_embeddings = self.target_embeddings[target_id]  # [N, hidden_size]
        context_labels = self.target_labels[target_id]  # [N]
        
        # Compute similarities
        similarities = F.cosine_similarity(
            query_embedding.unsqueeze(0),  # [1, hidden_size]
            context_embeddings  # [N, hidden_size]
        )  # [N]
        
        # Temperature scaling for more focused retrieval
        scaled_similarities = similarities / temperature
        
        # Get top-k most similar
        top_k = min(top_k, len(similarities))
        top_similarities, top_indices = torch.topk(scaled_similarities, top_k)
        
        # Retrieve corresponding embeddings and labels
        retrieved_embeddings = context_embeddings[top_indices]  # [top_k, hidden_size]
        retrieved_labels = context_labels[top_indices]  # [top_k]
        
        # Convert similarities to retrieval confidence (0-1 range)
        retrieval_confidence = torch.softmax(top_similarities, dim=0)
        
        return retrieved_embeddings, retrieved_labels, retrieval_confidence


class LigandReasoner(nn.Module):
    """
    Neural reasoning module that aggregates evidence from retrieved contexts
    
    This module reasons over retrieved similar ligands to make predictions.
    """
    
    def __init__(self, 
                 hidden_size: int = 512,
                 num_reasoning_layers: int = 3,
                 num_attention_heads: int = 8,
                 evidence_fusion_type: str = "attention"):
        super().__init__()
        
        self.hidden_size = hidden_size
        self.evidence_fusion_type = evidence_fusion_type
        
        # Query-context cross-attention (like RAG reader)
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=num_attention_heads,
            dropout=0.1,
            batch_first=True
        )
        
        # Multi-layer reasoning over evidence
        self.reasoning_layers = nn.ModuleList([
            nn.TransformerEncoderLayer(
                d_model=hidden_size,
                nhead=num_attention_heads,
                dim_feedforward=hidden_size * 2,
                dropout=0.1,
                activation='gelu',
                batch_first=True,
                norm_first=True
            ) for _ in range(num_reasoning_layers)
        ])
        
        # Evidence aggregation mechanisms
        if evidence_fusion_type == "attention":
            self.evidence_attention = nn.Sequential(
                nn.Linear(hidden_size, hidden_size // 2),
                nn.Tanh(),
                nn.Linear(hidden_size // 2, 1)
            )
        elif evidence_fusion_type == "weighted_sum":
            self.evidence_weights = nn.Linear(hidden_size + 1, 1)  # +1 for label
            
        # Final reasoning and prediction
        self.final_reasoner = nn.Sequential(
            nn.LayerNorm(hidden_size * 2),  # Query + aggregated evidence
            nn.Linear(hidden_size * 2, hidden_size),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.GELU(),
            nn.Linear(hidden_size // 2, 1)
        )
        
        # Confidence estimation
        self.confidence_estimator = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 1),
            nn.Sigmoid()
        )
        
    def forward(self, 
                query_embedding: torch.Tensor,
                context_embeddings: torch.Tensor,
                context_labels: torch.Tensor,
                retrieval_scores: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, Dict]:
        """
        Context-aware reasoning over retrieved context ligands
        
        Args:
            query_embedding: [B, hidden_size] - query ligand embeddings
            context_embeddings: [B, K, hidden_size] - retrieved context embeddings
            context_labels: [B, K] - labels of retrieved contexts
            retrieval_scores: [B, K] - retrieval confidence scores
            
        Returns:
            logits: [B, 1] - final predictions
            confidence: [B, 1] - prediction confidence
            reasoning_info: Dict with intermediate results
        """
        batch_size, num_contexts, hidden_size = context_embeddings.shape
        
        # Cross-attention between query and retrieved contexts
        query_expanded = query_embedding.unsqueeze(1)  # [B, 1, hidden_size]
        
        attended_query, attention_weights = self.cross_attention(
            query_expanded,  # Query
            context_embeddings,  # Key
            context_embeddings   # Value
        )  # [B, 1, hidden_size], [B, 1, K]
        
        attended_query = attended_query.squeeze(1)  # [B, hidden_size]
        attention_weights = attention_weights.squeeze(1)  # [B, K]
        
        # Multi-layer reasoning over contexts
        reasoning_input = context_embeddings  # [B, K, hidden_size]
        
        for layer in self.reasoning_layers:
            reasoning_input = layer(reasoning_input)  # Enhanced context representations
            
        # Evidence aggregation
        if self.evidence_fusion_type == "attention":
            # Attention-weighted aggregation of evidence
            evidence_scores = self.evidence_attention(reasoning_input).squeeze(-1)  # [B, K]
            evidence_weights = F.softmax(evidence_scores, dim=-1)  # [B, K]
        elif self.evidence_fusion_type == "weighted_sum":
            # Use both embeddings and labels for weighting
            label_features = context_labels.unsqueeze(-1)  # [B, K, 1]
            weight_input = torch.cat([reasoning_input, label_features], dim=-1)  # [B, K, hidden_size+1]
            evidence_weights = torch.sigmoid(self.evidence_weights(weight_input)).squeeze(-1)  # [B, K]
        else:
            # Simple average
            evidence_weights = torch.ones_like(retrieval_scores) / num_contexts
            
        # Combine with retrieval scores
        combined_weights = evidence_weights * retrieval_scores  # [B, K]
        combined_weights = F.softmax(combined_weights, dim=-1)  # [B, K]
        
        # Aggregate evidence
        aggregated_evidence = torch.sum(
            reasoning_input * combined_weights.unsqueeze(-1), dim=1
        )  # [B, hidden_size]
        
        # Final reasoning: combine query and aggregated evidence
        final_input = torch.cat([query_embedding, aggregated_evidence], dim=-1)  # [B, hidden_size*2]
        
        logits = self.final_reasoner(final_input)  # [B, 1]
        confidence = self.confidence_estimator(final_input)  # [B, 1]
        
        # Prepare reasoning information
        reasoning_info = {
            'attention_weights': attention_weights,  # Query-context attention
            'evidence_weights': evidence_weights,    # Evidence importance
            'combined_weights': combined_weights,    # Final retrieval weights
            'aggregated_evidence': aggregated_evidence,
            'retrieved_labels': context_labels,
            'retrieval_scores': retrieval_scores
        }
        
        return logits, confidence, reasoning_info


class ContextModule(nn.Module):
    """
    Complete context-aware module for ligand screening
    
    This module implements the full context-aware pipeline:
    1. Context Bank: Stores and curates known ligands
    2. Retrieval: Finds relevant similar ligands (optional for small context sets)
    3. Reasoning: Aggregates evidence for prediction
    """
    
    def __init__(self, 
                 hidden_size: int = 512,
                 max_context_size: int = 100,
                 retrieval_top_k: int = 10,
                 min_actives: int = 1,
                 min_inactives: int = 1,
                 diversity_threshold: float = 0.8,
                 num_reasoning_layers: int = 3,
                 num_attention_heads: int = 8,
                 evidence_fusion_type: str = "attention",
                 temperature: float = 0.1,
                 use_all_contexts: bool = False,  # NEW: Skip retrieval for small context sets
                 **kwargs):
        super().__init__()
        
        self.hidden_size = hidden_size
        self.retrieval_top_k = retrieval_top_k
        self.temperature = temperature
        self.use_all_contexts = use_all_contexts  # NEW parameter
        
        # Context bank for storing and retrieving ligands
        self.context_bank = AdaptiveContextBank(
            max_context_size=max_context_size,
            min_actives=min_actives,
            min_inactives=min_inactives,
            diversity_threshold=diversity_threshold
        )
        
        # Neural reasoner for evidence aggregation
        self.reasoner = LigandReasoner(
            hidden_size=hidden_size,
            num_reasoning_layers=num_reasoning_layers,
            num_attention_heads=num_attention_heads,
            evidence_fusion_type=evidence_fusion_type
        )
        
        # Optional: Embedding enhancement for better retrieval
        self.embedding_enhancer = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.GELU(),
            nn.Linear(hidden_size, hidden_size)
        )
        
    def register_target_contexts(self, 
                                target_id: str,
                                context_embeddings: torch.Tensor,
                                context_labels: torch.Tensor,
                                **kwargs):
        """Register known ligands for a target"""
        # Enhance embeddings for better retrieval
        enhanced_embeddings = self.embedding_enhancer(context_embeddings)
        
        self.context_bank.add_target_context(
            target_id=target_id,
            embeddings=enhanced_embeddings,
            labels=context_labels,
            **kwargs
        )
        
    def forward(self, 
                query_embeddings: torch.Tensor,
                target_id: str,
                context_embeddings: Optional[torch.Tensor] = None,
                context_labels: Optional[torch.Tensor] = None,
                return_reasoning_info: bool = False) -> Union[torch.Tensor, Tuple[torch.Tensor, Dict]]:
        """
        Context-aware prediction
        
        Args:
            query_embeddings: [B, hidden_size] - query ligand embeddings
            target_id: Target identifier for context processing
            context_embeddings: [N, hidden_size] - context embeddings (optional, if not using registration)
            context_labels: [N] - context labels (optional, if not using registration)
            return_reasoning_info: Whether to return detailed reasoning information
            
        Returns:
            logits: [B, 1] - context-aware predictions
            reasoning_info: Optional detailed reasoning information
        """
        batch_size = query_embeddings.shape[0]
        device = query_embeddings.device
        
        # Enhance query embeddings
        enhanced_queries = self.embedding_enhancer(query_embeddings)
        
        # Use provided context or fallback to registered context
        if context_embeddings is not None and context_labels is not None:
            # Use provided context directly - NEW: simplified path for redocked ligands
            enhanced_context_embeddings = self.embedding_enhancer(context_embeddings)
            
            if self.use_all_contexts:
                # NEW: Use all contexts without retrieval (for redocked crystallized ligands)
                num_contexts = enhanced_context_embeddings.shape[0]
                all_context_embeddings = enhanced_context_embeddings.unsqueeze(0).expand(batch_size, -1, -1)  # [B, N, hidden_size]
                all_context_labels = context_labels.unsqueeze(0).expand(batch_size, -1)  # [B, N]
                # Create uniform retrieval scores since no actual retrieval happened
                all_retrieval_scores = torch.ones(batch_size, num_contexts, device=device) / num_contexts  # [B, N]
                
                logging.info(f"Using all {num_contexts} contexts without retrieval for target {target_id}")
            else:
                # Original retrieval-based approach
                all_context_embeddings = [enhanced_context_embeddings for _ in range(batch_size)]
                all_context_labels = [context_labels for _ in range(batch_size)]
                all_retrieval_scores = []
                
                for i in range(batch_size):
                    query_emb = enhanced_queries[i]  # [hidden_size]
                    
                    # Compute similarities manually since we're not using context bank
                    similarities = F.cosine_similarity(
                        query_emb.unsqueeze(0),  # [1, hidden_size]
                        enhanced_context_embeddings  # [N, hidden_size]
                    )  # [N]
                    
                    # Get top-k
                    top_k = min(self.retrieval_top_k, len(similarities))
                    scaled_similarities = similarities / self.temperature
                    top_similarities, top_indices = torch.topk(scaled_similarities, top_k)
                    retrieval_confidence = torch.softmax(top_similarities, dim=0)
                    
                    # Select top-k contexts
                    all_context_embeddings[i] = enhanced_context_embeddings[top_indices]
                    all_context_labels[i] = context_labels[top_indices]
                    all_retrieval_scores.append(retrieval_confidence)
                
                # Stack to batch tensors
                context_embeddings = torch.stack(all_context_embeddings)  # [B, K, hidden_size]
                context_labels = torch.stack(all_context_labels)  # [B, K]
                all_retrieval_scores = torch.stack(all_retrieval_scores)  # [B, K]
                
        else:
            # Fallback to registered context retrieval (original path)
            all_context_embeddings = []
            all_context_labels = []
            all_retrieval_scores = []
            
            for i in range(batch_size):
                query_emb = enhanced_queries[i]  # [hidden_size]
                
                if self.use_all_contexts:
                    # NEW: Use all registered contexts without retrieval
                    if target_id not in self.context_bank.target_embeddings:
                        raise ValueError(f"No context available for target: {target_id}")
                    
                    ctx_embs = self.context_bank.target_embeddings[target_id]  # [N, hidden_size]
                    ctx_labels = self.context_bank.target_labels[target_id]  # [N]
                    # Uniform scores
                    ret_scores = torch.ones(len(ctx_embs), device=device) / len(ctx_embs)
                else:
                    # Retrieve top-k similar contexts
                    ctx_embs, ctx_labels, ret_scores = self.context_bank.retrieve_relevant_contexts(
                        target_id=target_id,
                        query_embedding=query_emb,
                        top_k=self.retrieval_top_k,
                        temperature=self.temperature
                    )
                
                all_context_embeddings.append(ctx_embs)
                all_context_labels.append(ctx_labels)
                all_retrieval_scores.append(ret_scores)
            
            # Stack to batch tensors
            context_embeddings = torch.stack(all_context_embeddings)  # [B, K, hidden_size]
            context_labels = torch.stack(all_context_labels)  # [B, K]
            all_retrieval_scores = torch.stack(all_retrieval_scores)  # [B, K]
        
        # Reasoning over retrieved contexts
        logits, confidence, reasoning_info = self.reasoner(
            query_embedding=enhanced_queries,
            context_embeddings=context_embeddings,
            context_labels=context_labels,
            retrieval_scores=all_retrieval_scores
        )
        
        if return_reasoning_info:
            reasoning_info.update({
                'confidence': confidence,
                'target_id': target_id,
                'num_contexts_used': context_embeddings.shape[1],
                'used_all_contexts': self.use_all_contexts,
                'retrieval_top_k': self.retrieval_top_k if not self.use_all_contexts else context_embeddings.shape[1]
            })
            return logits, reasoning_info
        
        return logits