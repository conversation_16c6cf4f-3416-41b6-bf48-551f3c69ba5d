#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
EPT + Context Wrapper

This module creates a wrapper around the existing EPT framework to add
context-aware capabilities without modifying the original EPT code.

Key Design Principles:
1. Zero modification to existing EPT models
2. Load pretrained weights without conflicts
3. Add-on architecture for Stage 2 training
4. Context-aware reasoning for enhanced predictions
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Union
import utils.register as R
from context_module import ContextModule
from collections import namedtuple
import logging
from models.wrappers.predictor_noisy_norm import *


# Extended return value for context-enhanced predictions
ContextReturnValue = namedtuple(
    'ContextReturnValue',
    ['stage1_logits', 'stage2_logits', 'confidence', 
     'unit_repr', 'block_repr', 'graph_repr',
     'reasoning_info', 'batch_id', 'block_id']
)


class EPTContextWrapper(nn.Module):
    """
    Wrapper that combines EPT (Stage 1) with a Context Module (Stage 2)
    
    Architecture:
    - Stage 1: Original EPT model (PredictorNNNModel) - can be frozen
    - Stage 2: Context Module - trains on top of EPT embeddings
    
    This design allows:
    1. Loading pretrained EPT weights without modification
    2. Training Stage 2 independently with frozen Stage 1
    3. Context-aware screening with any ratio of actives/inactives
    """
    
    def __init__(self, 
                 ept_config: Dict,
                 context_config: Optional[Dict] = None,
                 freeze_stage1: bool = True,
                 use_stage2: bool = True):
        """
        Args:
            ept_config: Original EPT model configuration
            context_config: Context module configuration
            freeze_stage1: Whether to freeze EPT weights during Stage 2 training
            use_stage2: Whether to use context module
        """
        super().__init__()
        
        # Stage 1: Original EPT model (unchanged)
        self.stage1_model = R.construct(ept_config)
        self.freeze_stage1 = freeze_stage1
        self.use_stage2 = use_stage2
        
        # Freeze Stage 1 if specified
        if freeze_stage1:
            for param in self.stage1_model.parameters():
                param.requires_grad = False
            logging.info("Stage 1 EPT model frozen")
            
        # Stage 2: Context Module
        if use_stage2:
            if context_config is None:
                context_config = {
                    'hidden_size': ept_config['encoder']['hidden_size'],
                    'max_context_size': 100,
                    'retrieval_top_k': 10,
                    'use_all_contexts': False  # Default to original behavior
                }
            
            self.stage2_model = ContextModule(**context_config)
            logging.info(f"Stage 2 Context module initialized with config: {context_config}")
        else:
            self.stage2_model = None
            
        # Store hidden size for convenience
        self.hidden_size = ept_config['encoder']['hidden_size']
        
        # No registration needed - context passed directly in forward pass
        
    def load_stage1_checkpoint(self, checkpoint_path: str):
        """
        Load pretrained EPT checkpoint without conflicts
        
        Args:
            checkpoint_path: Path to EPT checkpoint file
        """
        checkpoint = torch.load(checkpoint_path, map_location='cpu',weights_only=False)
        
        # # Extract model state dict
        # if 'model_state_dict' in checkpoint:
        #     state_dict = checkpoint['model_state_dict']
        # elif 'state_dict' in checkpoint:
        #     state_dict = checkpoint['state_dict']
        # else:
        #     state_dict = checkpoint
            
        # # Load weights into Stage 1 model
        # missing_keys, unexpected_keys = self.stage1_model.load_state_dict(state_dict, strict=False)
        
        # if missing_keys:
        #     logging.warning(f"Missing keys when loading Stage 1: {missing_keys}")
        # if unexpected_keys:
        #     logging.warning(f"Unexpected keys when loading Stage 1: {unexpected_keys}")
            
        logging.info(f"Loaded Stage 1 EPT model from: {checkpoint_path}")
        
    def _process_context_data(self, context_data_list: List[Dict]) -> torch.Tensor:
        """Process context ligand data to embeddings on-the-fly"""
        context_embeddings = []
        
        self.stage1_model.eval()
        with torch.no_grad():
            for ligand_data in context_data_list:
                # Prepare single ligand batch
                batch = self._prepare_single_ligand_batch(ligand_data)
                
                # Get Stage 1 embeddings
                ret = self.stage1_model(**batch)
                
                # Use block-level representation for context
                if hasattr(ret, 'block_repr'):
                    # Fallback to mean-pooled block representation
                    embedding = ret.block_repr.mean(dim=0)
                elif hasattr(ret, 'graph_repr'):
                    embedding = ret.graph_repr.squeeze(0)  # Remove batch dimension
                else:
                    raise ValueError("No suitable embedding found in Stage 1 output")
                    
                context_embeddings.append(embedding)
        
        # Stack embeddings
        return torch.stack(context_embeddings)  # [N, hidden_size]
        
    def _prepare_single_ligand_batch(self, ligand_data: Dict) -> Dict:
        """Convert single ligand data to a batch-of-one for the EPT model."""
        batch = {}
        # Define the mapping from expected model args to keys in the data file
        key_map = {
            'X': 'X',
            'Z': 'A',
            'B': 'B',
            'atom_positions': 'atom_positions',
            'block_lengths': 'block_lengths',
            'lengths': 'lengths',
            'segment_ids': 'segment_ids'
        }

        for model_arg, data_key in key_map.items():
            value = ligand_data.get(data_key)
            if value is not None:
                batch[model_arg] = torch.tensor(value).unsqueeze(0)
        
        return batch
        
    def forward_stage1_only(self, **kwargs) -> namedtuple:
        """Forward pass through Stage 1 EPT model only"""
        return self.stage1_model(**kwargs)
        
    def forward_stage2_only(self, 
                           query_embeddings: torch.Tensor, 
                           target_id: str,
                           **kwargs) -> Tuple[torch.Tensor, Dict]:
        """Forward pass through Stage 2 context module only"""
        if not self.use_stage2:
            raise ValueError("Stage 2 not enabled")
            
        return self.stage2_model(
            query_embeddings=query_embeddings,
            target_id=target_id,
            return_reasoning_info=True,
            **kwargs
        )
        
    def forward(self, 
                target_id: Optional[str] = None,
                context_data: Optional[List] = None,
                context_labels: Optional[List] = None,
                return_reasoning_info: bool = False,
                **kwargs) -> ContextReturnValue:
        """
        Complete forward pass through both stages
        
        Args:
            target_id: Target identifier for Stage 2 context processing
            context_data: List of context ligand data for each sample in batch
            context_labels: List of context labels for each sample in batch
            return_reasoning_info: Whether to return detailed reasoning
            **kwargs: EPT model inputs (Z, B, A, etc.)
            
        Returns:
            ContextReturnValue with both stage 1 and stage 2 results
        """
        # Stage 1: EPT forward pass
        stage1_ret = self.stage1_model(**kwargs)
        
        stage1_logits = getattr(stage1_ret, 'pred_logits', None)
        stage2_logits = None
        confidence = None
        reasoning_info = None
        
        # Stage 2: context-aware enhancement
        if self.use_stage2 and target_id is not None and context_data is not None:
            # Extract query embeddings for Stage 2 using block representations
            if hasattr(stage1_ret, 'block_repr'):
                # Use mean-pooled block representations as specified.
                batch_id = getattr(stage1_ret, 'batch_id', None)
                if batch_id is not None:
                    # Proper pooling over blocks for each sample in the batch
                    from torch_scatter import scatter_mean
                    query_embeddings = scatter_mean(stage1_ret.block_repr, batch_id, dim=0)
                else:
                    # Fallback for safety, though batch_id should always be present
                    query_embeddings = stage1_ret.block_repr.mean(dim=0, keepdim=True)
            elif hasattr(stage1_ret, 'graph_repr'):
                # Fallback to graph representation if block_repr is not available
                query_embeddings = stage1_ret.graph_repr
            else:
                logging.warning("No suitable embeddings for Stage 2, using Stage 1 only")
                query_embeddings = None
                
            if query_embeddings is not None:
                try:
                    # Process context data on-the-fly
                    context_embeddings = self._process_context_data(context_data[0])  # Use first sample's context
                    context_labels_tensor = torch.tensor(context_labels[0], dtype=torch.long)
                    
                    # Direct forward through Stage 2 with context
                    stage2_logits, reasoning_info = self.stage2_model(
                        query_embeddings=query_embeddings,
                        target_id=target_id,
                        context_embeddings=context_embeddings,
                        context_labels=context_labels_tensor,
                        return_reasoning_info=True
                    )
                    
                    confidence = reasoning_info.get('confidence', None)
                    
                    if not return_reasoning_info:
                        reasoning_info = None
                        
                except Exception as e:
                    logging.warning(f"Stage 2 forward failed: {e}, using Stage 1 only")
                    
        return ContextReturnValue(
            stage1_logits=stage1_logits,
            stage2_logits=stage2_logits,
            confidence=confidence,
            unit_repr=getattr(stage1_ret, 'unit_repr', None),
            block_repr=getattr(stage1_ret, 'block_repr', None),
            graph_repr=getattr(stage1_ret, 'graph_repr', None),
            reasoning_info=reasoning_info,
            batch_id=getattr(stage1_ret, 'batch_id', None),
            block_id=getattr(stage1_ret, 'block_id', None)
        )
        
    def get_predictions(self, 
                       target_id: Optional[str] = None,
                       use_stage2: bool = True,
                       **kwargs) -> torch.Tensor:
        """
        Get final predictions with automatic stage selection
        
        Args:
            target_id: Target ID for context-aware prediction
            use_stage2: Whether to use Stage 2 if available
            **kwargs: EPT model inputs
            
        Returns:
            predictions: [batch_size, 1] - final probability scores
        """
        ret = self.forward(target_id=target_id if use_stage2 else None, **kwargs)
        
        # Choose best available prediction
        if ret.stage2_logits is not None and use_stage2:
            logits = ret.stage2_logits
        elif ret.stage1_logits is not None:
            logits = ret.stage1_logits
        else:
            raise ValueError("No predictions available")
            
        return torch.sigmoid(logits)
        
    def set_stage1_mode(self, mode: str):
        """Set Stage 1 model mode ('train' or 'eval')"""
        if mode == 'train':
            if not self.freeze_stage1:
                self.stage1_model.train()
        elif mode == 'eval':
            self.stage1_model.eval()
        else:
            raise ValueError(f"Invalid mode: {mode}")
            
    def set_stage2_mode(self, mode: str):
        """Set Stage 2 model mode ('train' or 'eval')"""
        if self.use_stage2:
            if mode == 'train':
                self.stage2_model.train()
            elif mode == 'eval':
                self.stage2_model.eval()
            else:
                raise ValueError(f"Invalid mode: {mode}")
                
    def get_trainable_parameters(self, stage: str = 'stage2') -> List[nn.Parameter]:
        """Get trainable parameters for specified stage"""
        if stage == 'stage1':
            if self.freeze_stage1:
                return []
            else:
                return [p for p in self.stage1_model.parameters() if p.requires_grad]
        elif stage == 'stage2':
            if self.use_stage2:
                return [p for p in self.stage2_model.parameters() if p.requires_grad]
            else:
                return []
        elif stage == 'both':
            params = []
            if not self.freeze_stage1:
                params.extend(self.get_trainable_parameters('stage1'))
            if self.use_stage2:
                params.extend(self.get_trainable_parameters('stage2'))
            return params
        else:
            raise ValueError(f"Invalid stage: {stage}")
            
    def save_checkpoint(self, path: str, epoch: int, optimizer_state: Optional[Dict] = None):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'stage1_state_dict': self.stage1_model.state_dict(),
            'freeze_stage1': self.freeze_stage1,
            'use_stage2': self.use_stage2,
            'hidden_size': self.hidden_size,
        }
        
        if self.use_stage2:
            checkpoint['stage2_state_dict'] = self.stage2_model.state_dict()
            
        if optimizer_state:
            checkpoint['optimizer_state_dict'] = optimizer_state
            
        torch.save(checkpoint, path)
        logging.info(f"Saved checkpoint to: {path}")
        
    def load_checkpoint(self, path: str, load_optimizer: bool = False):
        """Load model checkpoint"""
        checkpoint = torch.load(path, map_location='cpu')
        
        # Load Stage 1
        if 'stage1_state_dict' in checkpoint:
            self.stage1_model.load_state_dict(checkpoint['stage1_state_dict'])
            
        # Load Stage 2
        if self.use_stage2 and 'stage2_state_dict' in checkpoint:
            self.stage2_model.load_state_dict(checkpoint['stage2_state_dict'])
            
        # No registration metadata needed
            
        logging.info(f"Loaded checkpoint from: {path}")
        
        if load_optimizer and 'optimizer_state_dict' in checkpoint:
            return checkpoint['optimizer_state_dict']
        return None