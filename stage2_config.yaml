# Stage 2 Context-Aware Training Configuration

# EPT Model Configuration (Stage 1 - will be frozen)
ept_model:
  class: PredictorNNNModel
  sigma_begin: 0.04
  sigma_end: 0.04
  n_noise_level: 1
  rot_sigma_begin: 0.1
  rot_sigma_end: 0.1
  rot_n_noise_level: 1
  noise_type: none
  agg_type: graph  # Use graph-level embeddings for context
  denoise_loss_scale: 0.1
  
  encoder:
    class: XTransEncoderAct
    hidden_size: 512
    ffn_size: 512
    n_rbf: 64
    cutoff: 10.0
    n_layers: 6
    n_head: 8
    edge_size: 64
    use_edge_feat: true
    sparse_k: 4
    pre_norm: true
    efficient: true
    vector_act: layernorm
    local_mask: true

  graph_constructor:
    class: GraphConstructor
    node_layers:
      - class: ContinuousEmbedding
        num_classes: 121
        embed_size: 512
        level: unit
      - class: ContinuousEmbedding
        num_classes: 142
        embed_size: 512
        level: block
      # - class: PositionEncoding
      #   max_position: 14
      #   embed_size: 512
      #   merge: add
      #   level: unit
      #   omit_mol: false
      - class: ScatterBlockFeatures
        merge: add
    edge_layers:
      - class: RadialEdge
        cutoff: 10.0
        topo_cutoff: 1.6
        scope: both
        self_loop: true
    edge_embed_size: 64

# Context Module Configuration (Stage 2 - trainable)
context_model:
  hidden_size: 512  # Must match EPT encoder hidden_size
  max_context_size: 100  # Maximum context ligands per target
  retrieval_top_k: 15    # Number of most similar contexts to retrieve (ignored if use_all_contexts=true)
  
  # NEW: Simplified mode for redocked crystallized ligands
  use_all_contexts: true  # Skip similarity-based retrieval, use all available contexts
  
  # Context bank parameters
  min_actives: 3         # Minimum active ligands required per target
  min_inactives: 3       # Minimum inactive ligands required per target
  diversity_threshold: 0.8  # Similarity threshold for diversity filtering
  
  # Context reasoner parameters
  num_reasoning_layers: 3
  num_attention_heads: 8
  evidence_fusion_type: "attention"  # "attention", "weighted_sum", or "average"
  temperature: 0.1       # Temperature for similarity scaling (ignored if use_all_contexts=true)

# Data Configuration
data:
  train_path: "/home/<USER>/colossus_code/EPT/stage2_data_blocks/train"
  val_path: "/home/<USER>/colossus_code/EPT/stage2_data_blocks/val"
  num_workers: 0
  
  # Context configuration for stage2 data
  context_config:
    min_actives: 2         # Minimum actives required for valid UniProt target
    min_inactives: 2       # Minimum inactives required for valid UniProt target
    min_total: 5           # Minimum total samples required for valid UniProt target
    max_context_size: 20   # Maximum context size per training sample

# Training Configuration
training:
  epochs: 50
  batch_size: 16         # Smaller batch size for context-aware training
  val_batch_size: 32
  learning_rate: 1.0e-4  # Lower LR for fine-tuning
  weight_decay: 1.0e-5
  
  # Target-aware sampling parameters
  drop_last: false       # Keep incomplete batches
  min_samples_per_target: 1  # Minimum samples per target to include
  
  # Class balancing
  pos_weight: null       # Auto-compute from data if null
  
  # Optimization
  lr_decay_factor: 0.8
  lr_patience: 5
  grad_clip: 1.0         # Gradient clipping

# Output Configuration
output:
  save_dir: "/home/<USER>/colossus_code/EPT/stage2/stage2_checkpoints"
  save_freq: 1           # Save checkpoint every N epochs
  
# Logging Configuration
logging:
  level: "INFO"
  use_wandb: false       # Set to true to use Weights & Biases
  wandb_project: "ept_context_screening"
  exp_name: "stage2_context_training"

# Checkpoint path (will be overridden by command line)
stage1_checkpoint: "/path/to/stage1/checkpoint.ckpt"